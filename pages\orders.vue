<template>
	<view class="orders-page">
		<view class="page-header">
			<text class="header-title">订单</text>
		</view>
		
		<view class="page-content">
			<view class="content-card">
				<text class="page-title">这是订单页面</text>
				<text class="page-desc">查看和管理您的订单</text>
			</view>
			
			<!-- 订单状态选项卡 -->
			<view class="order-tabs">
				<view class="tab-item active">
					<text class="tab-text">全部</text>
				</view>
				<view class="tab-item">
					<text class="tab-text">待付款</text>
				</view>
				<view class="tab-item">
					<text class="tab-text">进行中</text>
				</view>
				<view class="tab-item">
					<text class="tab-text">已完成</text>
				</view>
			</view>
			
			<!-- 订单列表示例 -->
			<view class="order-list">
				<view class="order-item">
					<view class="order-header">
						<text class="order-id">订单号: #12345</text>
						<text class="order-status">进行中</text>
					</view>
					<view class="order-content">
						<text class="order-service">游戏陪玩服务</text>
						<text class="order-price">¥50.00</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部TabBar导航 -->
		<view class="tabbar">
			<view class="tabbar-item" @click="switchTab('index')">
				<image class="tabbar-icon" src="/static/icon/shouye.png"></image>
				<text class="tabbar-text">首页</text>
			</view>
			<view class="tabbar-item" @click="switchTab('category')">
				<image class="tabbar-icon" src="/static/icon/fenlei.png"></image>
				<text class="tabbar-text">分类</text>
			</view>
			<view class="tabbar-item active" @click="switchTab('orders')">
				<image class="tabbar-icon" src="/static/icon/dingdan_active.png"></image>
				<text class="tabbar-text active">订单</text>
			</view>
			<view class="tabbar-item" @click="switchTab('profile')">
				<image class="tabbar-icon" src="/static/icon/my.png"></image>
				<text class="tabbar-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'OrdersPage',
		data() {
			return {

			}
		},
		methods: {
			// TabBar切换方法
			switchTab(tabName) {
				console.log('切换到:', tabName)
				switch(tabName) {
					case 'index':
						uni.switchTab({
							url: '/pages/index'
						})
						break
					case 'category':
						uni.switchTab({
							url: '/pages/category'
						})
						break
					case 'orders':
						// 当前就是订单页，不需要跳转
						break
					case 'profile':
						uni.switchTab({
							url: '/pages/profile'
						})
						break
				}
			}
		}
	}
</script>

<style scoped>
	.orders-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 100rpx; /* 为底部TabBar留出空间 */
	}
	
	.page-header {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
		padding: 60rpx 40rpx 40rpx;
		text-align: center;
	}
	
	.header-title {
		color: white;
		font-size: 48rpx;
		font-weight: bold;
	}
	
	.page-content {
		padding: 40rpx;
	}
	
	.content-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 40rpx;
	}
	
	.page-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.page-desc {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
	
	.order-tabs {
		display: flex;
		background-color: white;
		border-radius: 20rpx;
		margin-bottom: 40rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.tab-item {
		flex: 1;
		padding: 30rpx 20rpx;
		text-align: center;
		background-color: white;
	}
	
	.tab-item.active {
		background-color: #667eea;
	}
	
	.tab-text {
		font-size: 28rpx;
		color: #666;
	}
	
	.tab-item.active .tab-text {
		color: white;
		font-weight: bold;
	}
	
	.order-list {
		
	}
	
	.order-item {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.order-id {
		font-size: 28rpx;
		color: #333;
	}
	
	.order-status {
		font-size: 24rpx;
		color: #667eea;
		background-color: #f0f2ff;
		padding: 10rpx 20rpx;
		border-radius: 20rpx;
	}
	
	.order-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.order-service {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}
	
	.order-price {
		font-size: 32rpx;
		color: #ff6b6b;
		font-weight: bold;
	}

	/* 底部TabBar样式 */
	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #e5e5e5;
		display: flex;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		transition: all 0.3s ease;
	}

	.tabbar-item.active {
		transform: scale(1.05);
	}

	.tabbar-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}

	.tabbar-text {
		font-size: 24rpx;
		color: #999999;
		transition: color 0.3s ease;
	}

	.tabbar-text.active {
		color: #007AFF;
		font-weight: 500;
	}
</style>
