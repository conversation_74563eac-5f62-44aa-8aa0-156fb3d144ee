<template>
	<view class="category-page">
		<view class="page-header">
			<text class="header-title">分类</text>
		</view>
		
		<view class="page-content">
			<view class="content-card">
				<text class="page-title">这是分类页面</text>
				<text class="page-desc">游戏陪玩服务分类展示</text>
			</view>
			
			<!-- 分类列表 -->
			<view class="category-list">
				<view class="category-item">
					<view class="category-icon">🎮</view>
					<text class="category-name">游戏陪玩</text>
				</view>
				<view class="category-item">
					<view class="category-icon">🏆</view>
					<text class="category-name">技能代练</text>
				</view>
				<view class="category-item">
					<view class="category-icon">💬</view>
					<text class="category-name">语音聊天</text>
				</view>
				<view class="category-item">
					<view class="category-icon">🎯</view>
					<text class="category-name">专业指导</text>
				</view>
			</view>
		</view>

		<!-- 底部TabBar导航 -->
		<view class="tabbar">
			<view class="tabbar-item" @click="switchTab('index')">
				<image class="tabbar-icon" src="/static/icon/shouye.png"></image>
				<text class="tabbar-text">首页</text>
			</view>
			<view class="tabbar-item active" @click="switchTab('category')">
				<image class="tabbar-icon" src="/static/icon/fenlei_active.png"></image>
				<text class="tabbar-text active">分类</text>
			</view>
			<view class="tabbar-item" @click="switchTab('orders')">
				<image class="tabbar-icon" src="/static/icon/dingdan.png"></image>
				<text class="tabbar-text">订单</text>
			</view>
			<view class="tabbar-item" @click="switchTab('profile')">
				<image class="tabbar-icon" src="/static/icon/my.png"></image>
				<text class="tabbar-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CategoryPage',
		data() {
			return {

			}
		},
		methods: {
			// TabBar切换方法
			switchTab(tabName) {
				console.log('切换到:', tabName)
				switch(tabName) {
					case 'index':
						uni.switchTab({
							url: '/pages/index'
						})
						break
					case 'category':
						// 当前就是分类页，不需要跳转
						break
					case 'orders':
						uni.switchTab({
							url: '/pages/orders'
						})
						break
					case 'profile':
						uni.switchTab({
							url: '/pages/profile'
						})
						break
				}
			}
		}
	}
</script>

<style scoped>
	.category-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 100rpx; /* 为底部TabBar留出空间 */
	}
	
	.page-header {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
		padding: 60rpx 40rpx 40rpx;
		text-align: center;
	}
	
	.header-title {
		color: white;
		font-size: 48rpx;
		font-weight: bold;
	}
	
	.page-content {
		padding: 40rpx;
	}
	
	.content-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 40rpx;
	}
	
	.page-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.page-desc {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
	
	.category-list {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
	}
	
	.category-item {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.category-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.category-name {
		font-size: 28rpx;
		color: #333;
		display: block;
	}

	/* 底部TabBar样式 */
	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #e5e5e5;
		display: flex;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		transition: all 0.3s ease;
	}

	.tabbar-item.active {
		transform: scale(1.05);
	}

	.tabbar-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}

	.tabbar-text {
		font-size: 24rpx;
		color: #999999;
		transition: color 0.3s ease;
	}

	.tabbar-text.active {
		color: #007AFF;
		font-weight: 500;
	}
</style>
