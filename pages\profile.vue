<template>
	<view class="profile-page">
		<view class="page-header">
			<text class="header-title">我的</text>
		</view>
		
		<view class="page-content">
			<view class="content-card">
				<text class="page-title">这是我的页面</text>
				<text class="page-desc">个人中心和设置</text>
			</view>
			
			<!-- 用户信息 -->
			<view class="user-info">
				<view class="avatar">
					<text class="avatar-text">👤</text>
				</view>
				<view class="user-details">
					<text class="username">用户名</text>
					<text class="user-level">普通用户</text>
				</view>
			</view>
			
			<!-- 功能菜单 -->
			<view class="menu-list">
				<view class="menu-item">
					<view class="menu-icon">📊</view>
					<text class="menu-text">我的数据</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">💰</view>
					<text class="menu-text">我的钱包</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">⚙️</view>
					<text class="menu-text">设置</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item">
					<view class="menu-icon">❓</view>
					<text class="menu-text">帮助与反馈</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
		</view>

		<!-- 底部TabBar导航 -->
		<view class="tabbar">
			<view class="tabbar-item" @click="switchTab('index')">
				<image class="tabbar-icon" src="/static/icon/shouye.png"></image>
				<text class="tabbar-text">首页</text>
			</view>
			<view class="tabbar-item" @click="switchTab('category')">
				<image class="tabbar-icon" src="/static/icon/fenlei.png"></image>
				<text class="tabbar-text">分类</text>
			</view>
			<view class="tabbar-item" @click="switchTab('orders')">
				<image class="tabbar-icon" src="/static/icon/dingdan.png"></image>
				<text class="tabbar-text">订单</text>
			</view>
			<view class="tabbar-item active" @click="switchTab('profile')">
				<image class="tabbar-icon" src="/static/icon/my_active.png"></image>
				<text class="tabbar-text active">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ProfilePage',
		data() {
			return {

			}
		},
		methods: {
			// TabBar切换方法
			switchTab(tabName) {
				console.log('切换到:', tabName)
				switch(tabName) {
					case 'index':
						uni.switchTab({
							url: '/pages/index'
						})
						break
					case 'category':
						uni.switchTab({
							url: '/pages/category'
						})
						break
					case 'orders':
						uni.switchTab({
							url: '/pages/orders'
						})
						break
					case 'profile':
						// 当前就是我的页面，不需要跳转
						break
				}
			}
		}
	}
</script>

<style scoped>
	.profile-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 100rpx; /* 为底部TabBar留出空间 */
	}
	
	.page-header {
		background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
		padding: 60rpx 40rpx 40rpx;
		text-align: center;
	}
	
	.header-title {
		color: white;
		font-size: 48rpx;
		font-weight: bold;
	}
	
	.page-content {
		padding: 40rpx;
	}
	
	.content-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 40rpx;
	}
	
	.page-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.page-desc {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
	
	.user-info {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 40rpx;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background-color: #667eea;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30rpx;
	}
	
	.avatar-text {
		font-size: 60rpx;
		color: white;
	}
	
	.user-details {
		flex: 1;
	}
	
	.username {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.user-level {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
	
	.menu-list {
		background-color: white;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.menu-item {
		display: flex;
		align-items: center;
		padding: 40rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.menu-item:last-child {
		border-bottom: none;
	}
	
	.menu-icon {
		font-size: 40rpx;
		margin-right: 30rpx;
	}
	
	.menu-text {
		flex: 1;
		font-size: 32rpx;
		color: #333;
	}
	
	.menu-arrow {
		font-size: 32rpx;
		color: #ccc;
	}

	/* 底部TabBar样式 */
	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #e5e5e5;
		display: flex;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		transition: all 0.3s ease;
	}

	.tabbar-item.active {
		transform: scale(1.05);
	}

	.tabbar-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}

	.tabbar-text {
		font-size: 24rpx;
		color: #999999;
		transition: color 0.3s ease;
	}

	.tabbar-text.active {
		color: #007AFF;
		font-weight: 500;
	}
</style>
