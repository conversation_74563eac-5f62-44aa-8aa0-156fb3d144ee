# 三角洲陪玩后台管理系统 - API接口文档

## 基础配置

**服务器地址**: `http://**************:9998`

**请求头配置**:
```json
{
  "Content-Type": "application/json"
}
```

**响应格式**:
```json
{
  "code": 1,           // 1表示成功，其他表示失败
  "data": {},          // 响应数据
  "message": "string"  // 响应消息
}
```

## 1. 用户管理接口

### 1.1 获取用户列表
- **接口**: `GET /api/user`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 每页数量
- **响应**: 
```json
{
  "code": 1,
  "data": {
    "records": [
      {
        "id": "string",
        "nickname": "string",
        "avatar": "string",
        "phone": "string",
        "userIp": "string",
        "userType": 1,
        "joinTime": "string",
        "createTime": "string",
        "updateTime": "string"
      }
    ],
    "total": 0,
    "totalPages": 0
  }
}
```

### 1.2 搜索用户
- **接口**: `GET /api/user/search`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 每页数量
  - `userId`: 用户ID (可选)
  - `nickname`: 昵称 (可选)
  - `phone`: 手机号 (可选)
  - `userIp`: 用户IP (可选)
  - `userType`: 用户类型 (可选)

### 1.3 获取用户详情
- **接口**: `GET /api/user/{userId}`
- **参数**: 
  - `userId`: 用户ID

### 1.4 添加用户
- **接口**: `POST /api/user`
- **请求体**:
```json
{
  "nickname": "string",
  "avatar": "string",
  "phone": "string",
  "password": "string",
  "userIp": "string",
  "userType": 1,
  "joinTime": "string",
  "createTime": "string",
  "updateTime": "string"
}
```

### 1.5 更新用户
- **接口**: `PUT /api/user`
- **请求体**:
```json
{
  "id": "string",
  "nickname": "string",
  "avatar": "string",
  "phone": "string",
  "password": "string",
  "userIp": "string",
  "userType": 1
}
```

### 1.6 删除用户
- **接口**: `DELETE /api/user/{userId}`
- **参数**: 
  - `userId`: 用户ID

## 2. 管理员账号接口

### 2.1 获取管理员账号列表
- **接口**: `GET /api/admin-account`
- **参数**: 
  - `page`: 页码
  - `size`: 每页数量

### 2.2 按类型搜索管理员账号
- **接口**: `GET /api/admin-account/search`
- **参数**: 
  - `accountType`: 账号类型
  - `page`: 页码
  - `size`: 每页数量

### 2.3 添加管理员账号
- **接口**: `POST /api/admin-account`
- **请求体**:
```json
{
  "account": "string",
  "password": "string",
  "accountType": 1
}
```

### 2.4 更新管理员账号
- **接口**: `PUT /api/admin-account`
- **请求体**:
```json
{
  "id": "string",
  "account": "string",
  "password": "string",
  "accountType": 1
}
```

### 2.5 删除管理员账号
- **接口**: `DELETE /api/admin-account/{adminId}`
- **参数**: 
  - `adminId`: 管理员ID

## 3. 打手管理接口

### 3.1 搜索打手档案
- **接口**: `GET /api/player/search`
- **参数**: 
  - `userId`: 用户ID

### 3.2 添加打手档案
- **接口**: `POST /api/player`
- **请求体**:
```json
{
  "userId": "string",
  "gameId": "string",
  "gameLevel": "string",
  "hourlyRate": 0,
  "description": "string",
  "isAvailable": true,
  "totalOrders": 0,
  "rating": 0,
  "createTime": "string",
  "updateTime": "string"
}
```

### 3.3 删除打手档案
- **接口**: `DELETE /api/player/{playerId}`
- **参数**: 
  - `playerId`: 打手ID

## 4. 打手等级管理接口

### 4.1 获取打手等级列表
- **接口**: `GET /api/player-level`
- **参数**: 
  - `page`: 页码
  - `pageSize`: 每页数量

### 4.2 搜索打手等级
- **接口**: `GET /api/player-level/search`
- **参数**: 
  - `page`: 页码
  - `pageSize`: 每页数量
  - `levelName`: 等级名称 (可选)
  - `playerId`: 打手ID (可选)
  - `levelCode`: 等级代码 (可选)

### 4.3 通过用户ID获取打手等级
- **接口**: `GET /api/player-level/player/{userId}`
- **参数**: 
  - `userId`: 用户ID

### 4.4 添加打手等级
- **接口**: `POST /api/player-level`
- **请求体**:
```json
{
  "playerId": "string",
  "levelName": "string",
  "levelCode": 1,
  "currentExp": 0,
  "requiredExp": 0,
  "maxOrders": 0
}
```

### 4.5 更新打手等级
- **接口**: `PUT /api/player-level`
- **请求体**:
```json
{
  "id": "string",
  "playerId": "string",
  "levelName": "string",
  "levelCode": 1,
  "currentExp": 0,
  "requiredExp": 0,
  "maxOrders": 0
}
```

### 4.6 删除打手等级
- **接口**: `DELETE /api/player-level/{levelId}`
- **参数**: 
  - `levelId`: 等级记录ID

## 5. 标签管理接口

### 5.1 获取标签列表
- **接口**: `GET /tag`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 每页数量

### 5.2 搜索标签
- **接口**: `GET /tag/search`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 每页数量
  - `tagName`: 标签名称 (可选)

### 5.3 添加标签
- **接口**: `POST /tag`
- **请求体**:
```json
{
  "tagName": "string"
}
```

### 5.4 更新标签
- **接口**: `PUT /tag`
- **请求体**:
```json
{
  "id": "string",
  "tagName": "string"
}
```

### 5.5 删除标签
- **接口**: `DELETE /tag/{tagId}`
- **参数**: 
  - `tagId`: 标签ID

## 6. 押金管理接口

### 6.1 搜索押金提现记录
- **接口**: `GET /deposit-withdraw/search`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 每页数量
  - `auditStatus`: 审核状态 (可选)
  - `playerId`: 打手ID (可选)

### 6.2 更新审核状态
- **接口**: `PUT /deposit-withdraw`
- **请求体**:
```json
{
  "id": "string",
  "auditStatus": 1
}
```

### 6.3 删除押金提现记录
- **接口**: `DELETE /deposit-withdraw/{recordId}`
- **参数**: 
  - `recordId`: 记录ID

## 7. 合同管理接口

### 7.1 获取合同列表
- **接口**: `GET /contract`
- **参数**: 
  - `page`: 页码
  - `pageSize`: 每页数量

### 7.2 通过用户ID搜索合同
- **接口**: `GET /contract/player/{playerId}`
- **参数**: 
  - `playerId`: 打手ID
  - `page`: 页码
  - `pageSize`: 每页数量

### 7.3 添加合同
- **接口**: `POST /contract`
- **请求体**:
```json
{
  "playerId": "string",
  "idCard": "string",
  "realName": "string",
  "phone": "string",
  "contractFileUrl": "string"
}
```

### 7.4 更新合同
- **接口**: `PUT /contract`
- **请求体**:
```json
{
  "id": "string",
  "playerId": "string",
  "idCard": "string",
  "realName": "string",
  "phone": "string",
  "contractFileUrl": "string"
}
```

### 7.5 删除合同
- **接口**: `DELETE /contract/{contractId}`
- **参数**: 
  - `contractId`: 合同ID

## 8. 文件上传接口

### 8.1 文件上传
- **接口**: `POST /upload`
- **请求类型**: `multipart/form-data`
- **响应**:
```json
{
  "code": 1,
  "data": "http://**************:9998/uploads/filename.ext"
}
```

## 数据类型说明

### 用户类型 (userType)
- `1`: 普通用户
- `2`: 打手用户

### 等级代码 (levelCode)
- `1`: 初级
- `2`: 中级
- `3`: 高级
- `4`: 顶级

### 审核状态 (auditStatus)
- `1`: 待审核
- `2`: 审核通过
- `3`: 审核拒绝

### 账号类型 (accountType)
- `1`: 超级管理员
- `2`: 普通管理员

## 9. 商品管理接口

### 9.1 获取商品列表
- **接口**: `GET {{baseURL}}/products?pageNum=1&pageSize=10`
- **成功响应**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "total": 1,
        "records": [
            {
                "id": "e3cb4b4e-382d-4dfa-a7e0-343888dc5a73",
                "imageUrl": "商品图片URL",
                "productDetail": "商品详情（支持HTML富文本格式）",
                "productServer": "商品区服",
                "productIntro": "商品介绍（支持HTML富文本格式）",
                "weight": 100,
                "money": 9999.99,
                "createTime": "2025-08-20T00:04:58",
                "updateTime": "2025-08-20T00:04:58",
                "categoryId": ["tag2", "tag1"]
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "totalPages": 1
    }
}
```

### 9.2 按标签查询商品
- **接口**: `GET {{baseURL}}/products/category/{tagId}?pageNum=1&pageSize=10`
- **成功响应**: 格式同上

### 9.3 通过商品ID查询商品
- **接口**: `GET {{baseURL}}/product/{productId}`
- **参数**:
  - `productId`: 商品ID（UUID格式）
- **成功响应**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "id": "35d8891f-50cd-495f-84d3-e23dbc47ce93",
        "imageUrl": "商品图片URL",
        "productDetail": "商品详情（HTML格式）",
        "productServer": "商品区服",
        "productIntro": "商品介绍（HTML格式）",
        "weight": 100,
        "money": 9999.99,
        "createTime": "2025-08-20T00:04:58",
        "updateTime": "2025-08-20T00:04:58",
        "categoryId": ["tag2", "tag1"]
    }
}
```

### 9.4 新增商品
- **接口**: `PUT {{baseURL}}/products`
- **请求体**:
```json
{
    "imageUrl": "商品图片URL",
    "productDetail": "商品详情（HTML格式）",
    "productServer": "商品区服",
    "productIntro": "商品介绍（HTML格式）",
    "weight": 100,
    "categoryId": ["tag1", "tag2"],
    "money": 19.12
}
```
- **成功响应**: `{"code": 1, "msg": "success", "data": "商品添加成功"}`

### 9.5 编辑商品
- **接口**: `PUT {{baseURL}}/products/{productId}`
- **请求体**: 格式同新增商品
- **成功响应**: `{"code": 1, "msg": "success", "data": "商品修改成功"}`

### 9.6 删除商品
- **接口**: `DELETE {{baseURL}}/products/{productId}`
- **成功响应**: `{"code": 1, "msg": "success", "data": "商品删除成功"}`

### 9.7 富文本编辑器说明
- **商品详情 (productDetail)** 和 **商品介绍 (productIntro)** 字段支持HTML富文本格式
- 使用 **wangeditor v4.7.15** 富文本编辑器
- **支持的功能**：
  - 文本格式化（加粗、斜体、下划线、删除线）
  - 标题设置（H1-H6）
  - 字体和字号设置
  - 颜色设置（文字颜色、背景色）
  - 列表（有序列表、无序列表、待办事项）
  - 文本对齐（左对齐、居中、右对齐、两端对齐）
  - 链接插入
  - 图片上传（通过 `/upload` 接口）
  - 视频插入
  - 表格插入
  - 代码块
  - 引用
  - 表情符号
  - 分割线
  - 撤销/重做
  - 全屏编辑

### 9.8 图片上传配置
- **上传接口**: `POST http://**************:9998/upload`
- **文件限制**:
  - 支持格式：jpg, jpeg, png, gif, bmp
  - 文件大小：最大 2MB
  - 单次最多上传：5张图片
- **成功响应**: `{"code": 1, "msg": "success", "data": "图片URL"}`

## 10. 订单管理接口

### 10.1 获取订单列表
- **接口**: `GET {{baseURL}}/orders?page=1&pageSize=10`
- **成功响应**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "total": 1,
        "records": [
            {
                "id": "348468973043585024",
                "orderNo": "ORD1755612677414",
                "productId": "PROD2025015",
                "userId": "U1488867542365419531",
                "totalAmount": 258.00,
                "userTitle": "女士",
                "userPhone": "***********",
                "gameName": "和平精英",
                "gameServer": "iOS微信3区",
                "account": "wx876543",
                "password": "peacepass123",
                "showPassword": 1,
                "userMessage": "需要达到王牌段位",
                "channelSource": "小程序",
                "paymentTime": null,
                "playerInfo": "",
                "paymentStatus": 1,
                "playerStatus": 5,
                "paymentMethod": "",
                "remark": "",
                "startScreenshots": "",
                "finishScreenshots": "",
                "playerId": "P10005",
                "assignType": 1,
                "createTime": "2025-08-19T22:11:17",
                "updateTime": "2025-08-19T22:11:17"
            }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "totalPages": 1
    }
}
```

### 10.2 订单号精确搜索
- **接口**: `GET {{baseURL}}/orders/orderNo/{orderNo}`
- **示例**: `{{baseURL}}/orders/orderNo/ORD1755610987393`
- **成功响应**: 返回单个订单对象

### 10.3 复合条件搜索
- **接口**: `GET {{baseURL}}/orders/search?page=1&pageSize=10`
- **支持的搜索条件**（可组合使用）：
  - 支付状态：`&paymentStatus=1`
  - 打手完成状态：`&playerStatus=0`
  - 用户ID：`&userId={用户ID}`
  - 是否指定打手：`&assignType=1`
  - 游戏区服：`&gameServer={区服名称}`
  - 时间范围：`&startTime=2025-08-18T13:03:12&endTime=2025-08-19T13:03:12`
  - 渠道来源：`&channelSource={渠道名称}`

### 10.4 编辑订单
- **接口**: `PUT {{baseURL}}/orders`
- **请求体**: 必须包含 `id` 字段，其他字段可选修改
- **成功响应**: `{"code": 1, "msg": "success", "data": "修改订单成功"}`

### 10.5 删除订单
- **接口**: `DELETE {{baseURL}}/orders/{orderId}`
- **成功响应**: `{"code": 1, "msg": "success", "data": "删除订单成功"}`

### 10.6 字段说明
- **支付状态 (paymentStatus)**：1=未支付，2=待接单，3=待完成，4=已完成，5=已取消，6=已退款，7=未通过
- **打手状态 (playerStatus)**：0=待接取，1=进行中，2=已完成，3=待结算，4=已结算，5=未通过
- **指定打手 (assignType)**：1=指定，0=未指定
- **显示密码 (showPassword)**：1=显示，0=隐藏
- **截图字段格式**：`"[url1],[url2],[url3]"` 需要解析为图片数组
- **表格显示字段**：订单号显示 `id` 字段，商品显示 `productId` 字段，游戏信息显示 `gameName` 字段
- **交互功能**：商品ID支持双击复制到剪贴板，带有悬停提示和视觉反馈
- **金额计算**：订单金额通过商品详情接口获取单价，计算公式为：商品单价 × 下单数量

## 11. 系统配置管理

### 11.1 轮播图管理API接口

#### 11.1.1 获取轮播图列表
- **接口**: `GET {{baseURL}}/api/carousel?pageNum=1&pageSize=10`
- **排序规则**: 按权重（weight）从高到低自动排序
- **数据处理**: 支持多种响应格式（直接数组、分页格式、列表格式）

#### 11.1.2 添加轮播图
- **接口**: `POST {{baseURL}}/api/carousel`
- **请求参数**:
```json
{
  "name": "轮播图",
  "url": "图片URL地址",
  "weight": 2
}
```

#### 11.1.3 修改轮播图
- **接口**: `PUT {{baseURL}}/api/carousel`
- **请求参数**: 必须包含 `id` 字段

#### 11.1.4 删除轮播图
- **接口**: `DELETE {{baseURL}}/api/carousel/{id}`

#### 11.1.5 权重字段说明
- **weight字段**: 表示轮播图的权重/优先级（1-999）
- **排序逻辑**: 系统会根据权重值从高到低自动排序显示轮播图

### 11.2 其他配置管理

#### 11.2.1 配置项类型

**抢单延时配置：**
- **qiangdan1**: 初级打手延时（毫秒）
- **qiangdan2**: 中级打手延时（毫秒）
- **qiangdan3**: 高级打手延时（毫秒）
- **qiangdan4**: 顶级打手延时（毫秒）

**接单数量限制配置：**
- **max1**: 初级打手最多接单数
- **max2**: 中级打手最多接单数
- **max3**: 高级打手最多接单数
- **max4**: 顶级打手最多接单数

**等级升级经验配置：**
- **ol1**: 初级到中级所需经验值
- **ol2**: 中级到高级所需经验值
- **ol3**: 高级到顶级所需经验值

**提现时间配置：**
- **timemax**: 提现处理时间（小时）

**其他配置：**
- **h5lian**: H5链接设置
- **erweima**: 二维码图片
- **gonggao**: 公告通知
- **dashouerwei**: 打手内部群的二维码图片

#### 11.2.2 抢单延时配置处理
- **配置类型**: 数字输入，单位为毫秒
- **输入范围**: 0-999999毫秒
- **建议值**:
  - 初级打手(qiangdan1): 3000毫秒(3秒)
  - 中级打手(qiangdan2): 2000毫秒(2秒)
  - 高级打手(qiangdan3): 1000毫秒(1秒)
  - 顶级打手(qiangdan4): 500毫秒(0.5秒)
- **显示格式**: 表格中同时显示毫秒值和秒数转换

#### 11.2.3 图片配置处理
- **支持配置**: `erweima`、`dashouerwei`
- **上传功能**: 支持图片上传，自动保存图片URL到配置值
- **预览功能**: 表格中显示图片缩略图，支持点击预览
- **删除功能**: 支持删除已上传的图片

#### 11.2.3 配置接口
- **获取配置**: `GET {{baseURL}}/api/system-config/get-by-key/{configKey}`
- **更新配置**: `PUT {{baseURL}}/api/system-config/update`
- **添加配置**: `POST {{baseURL}}/api/system-config/add`

### 11.3 订单状态说明文档

#### 11.3.1 支付状态枚举值及说明
- **1 - 未支付**: 客户尚未完成付款
- **2 - 待接单**: 客户已付款，等待打手接单（超过10分钟无打手接单则自动取消订单，时间可在系统配置中自定义）
- **3 - 待完成**: 客户已付款且打手已接单，打手需上传登号截图
- **4 - 已完成**: 打手已上传结束截图，订单完成，客户可进行评价
- **5 - 已取消**: 订单被取消（仅限无打手接单时客户可主动取消，或系统超时自动取消）
- **6 - 已退款**: 客户投诉后，客服审核通过并执行退款
- **7 - 未通过**: 客户投诉后，客服审核未通过，订单状态恢复

#### 11.3.2 打手状态枚举值及说明
- **0 - 待接取**: 订单等待打手接单
- **1 - 进行中**: 打手已接单，需上传上机截图（3-5张，最少3张）
- **2 - 已完成**: 打手已上传打单完成截图
- **3 - 待结算**: 客服正在审批完成过程
- **4 - 已结算**: 客服审核通过，订单金额已进入打手余额
- **5 - 未通过**: 客户投诉后，客服审核认定打手责任，打手无法获得收益

### 11.4 渠道管理主列表功能

#### 11.4.1 主列表配置规范
- **配置键**: `qudao`
- **配置值**: JSON字符串数组格式，例如：`"[\"qudao1\",\"qudao2\"]"`
- **配置描述**: "打手等级权重"
- **用途**: 维护所有活跃渠道键的主列表

#### 11.4.2 自动更新逻辑
- **渠道添加时**: 自动将新渠道键添加到主列表，然后刷新渠道列表显示
- **渠道删除时**: 自动从主列表中移除对应渠道键，然后刷新渠道列表显示
- **刷新顺序**: 先更新主列表，再重新加载渠道配置，确保页面显示最新状态
- **错误处理**: 解析失败时初始化为空数组，主列表更新失败时仍会尝试刷新页面
- **幂等性**: 避免重复添加相同的渠道键

#### 11.4.3 API调用流程

**主列表更新流程:**
1. **获取主配置**: `GET {{baseURL}}/api/system-config/get-by-key/qudao`
2. **解析配置值**: 将JSON字符串解析为JavaScript数组
3. **更新数组**: 添加或移除渠道键
4. **保存配置**: `PUT {{baseURL}}/api/system-config/update` 或 `POST {{baseURL}}/api/system-config/add`

**动态加载流程:**
1. **获取主配置**: `GET {{baseURL}}/api/system-config/get-by-key/qudao`
2. **解析渠道键**: 从 `configValue` 解析出渠道键数组
3. **动态加载**: 遍历渠道键，调用 `GET {{baseURL}}/api/system-config/get-by-key/{channelKey}`
4. **渲染显示**: 将获取的配置数据添加到渠道列表中

#### 11.4.4 主配置示例
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "id": "349092621752340480",
        "configKey": "qudao",
        "configValue": "[\"qudao1\",\"qudao2\"]",
        "configDesc": "打手等级权重",
        "createTime": "2025-08-21T15:29:27",
        "updateTime": "2025-08-21T15:29:27"
    }
}
```

#### 11.4.5 渠道路径自动维护功能

**配置项规范:**
- **配置键**: `allqudao`
- **配置值**: JSON字符串数组格式，例如：`"[\"lujin1\",\"lujin2\",\"lujin3\",\"lujin4\"]"`
- **更新接口**: `PUT {{baseURL}}/api/system-config/update-by-key/allqudao`

**自动维护逻辑:**
- **渠道添加**: 自动将新渠道路径添加到 `allqudao` 配置
- **渠道删除**: 自动从 `allqudao` 配置中移除对应路径
- **渠道修改**: 如果路径发生变更，自动更新 `allqudao` 配置中的路径

**路径配置示例:**
```json
{
    "configKey": "allqudao",
    "configValue": "[\"lujin1\",\"lujin2\",\"lujin3\",\"lujin4\"]"
}
```

### 11.5 订单查看系统（uniapp分包页面）

#### 11.5.1 页面功能
- **文件位置**: `x/index.vue`
- **访问路径**: `http://localhost:8080/#/x/index`
- **功能定位**: 只读订单查看系统，基于OrderManagement.vue功能迁移
- **技术栈**: uniapp + Vue.js 2 + Element UI

#### 11.5.2 功能特性
- **订单列表**: 完整的订单表格显示，包含所有关键字段
- **搜索功能**: 订单号精确搜索和高级搜索
- **分页功能**: 支持分页浏览和页面大小调整
- **数据刷新**: 手动刷新和自动加载关联信息
- **商品ID复制**: 双击复制商品ID到剪贴板
- **订单详情**: 只读模式的订单详情查看

#### 11.5.3 功能限制
- **只读模式**: 移除所有编辑、删除功能
- **查看权限**: 仅支持数据查看和搜索
- **安全性**: 不包含任何数据修改操作

#### 11.5.4 API接口复用
- **订单列表**: `GET {{baseURL}}/orders?page={pageNum}&pageSize={pageSize}`
- **订单搜索**: `GET {{baseURL}}/orders/search`
- **用户信息**: `GET {{baseURL}}/users/{userId}`
- **打手信息**: `GET {{baseURL}}/players/{playerId}`
- **商品信息**: `GET {{baseURL}}/products/{productId}`

### 11.6 项目结构重构

#### 11.6.1 页面迁移和路由配置
- **管理后台迁移**: 原 `pages/index/index.vue` 迁移至 `admin/index.vue`
- **访问路径**: `http://localhost:8080/#/admin/index`
- **路径修复**: 组件导入路径从 `../../components/` 更新为 `../components/`

#### 11.6.2 商场首页创建
- **文件位置**: `pages/index/index.vue`
- **访问路径**: `http://localhost:8080/#/` (默认首页)
- **功能定位**: 商场首页，提供导航和基础展示

#### 11.6.3 页面导航关系
- **商场首页** → **管理后台**: `uni.navigateTo({ url: '/admin/index' })`
- **商场首页** → **订单查看**: `uni.navigateTo({ url: '/x/index' })`
- **管理后台**: 包含完整的后台管理功能
- **订单查看**: 只读模式的订单查看系统

#### 11.6.4 技术特性
- **响应式设计**: 适配不同屏幕尺寸
- **Element UI集成**: 保持统一的UI风格
- **uniapp路由**: 使用 `uni.navigateTo` 进行页面跳转
- **模块化结构**: 清晰的功能模块划分

### 11.7 动态渠道多站点系统

#### 11.7.1 系统架构
- **主站点**: `www.xxx.com` (默认商场首页)
- **渠道子站点**: `www.xxx.com/{channelPath}/#/pages/channel`
- **错误处理**: 无效渠道访问自动跳转到主页

#### 11.7.2 路径结构设计
- **主站点访问**: `www.xxx.com` → 显示标准商场首页
- **渠道站点访问**: `www.xxx.com/qudao1/` → 显示带渠道标识的商场首页
- **错误处理**: 无效渠道访问时自动跳转到主页

#### 11.7.3 URL访问模式
- **根域名访问**: `www.xxx.com` → 显示标准商场首页
- **渠道路径访问**: `www.xxx.com/qudao1/` → 显示带渠道标识的商场首页
- **无效渠道**: `www.xxx.com/invalidchannel/` → 自动跳转到主页

#### 11.7.4 渠道验证API

- **接口地址**: `{{baseURL}}/api/system-config/get-by-key/allqudao`
- **返回格式**:

```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "id": "349092621752340480",
        "configKey": "allqudao",
        "configValue": "[\"qudao1\",\"qudao3\",\"qudao4\",\"qudao5\"]",
        "configDesc": "渠道路径列表",
        "createTime": "2025-08-21T15:29:27",
        "updateTime": "2025-08-21T17:01:30"
    }
}
```

#### 11.7.4 核心组件功能

**channelValidator.js**:
- 渠道路径验证
- 有效渠道列表缓存
- URL路径提取和解析

**routeGuard.js**:
- 路由拦截和验证
- 无效渠道自动跳转到主页
- 路由守卫中间件

**channelRouter.js**:
- 应用启动时的渠道检测
- 页面显示时的渠道处理
- 渠道URL生成工具

#### 11.7.5 页面功能

**pages/index.vue**:

- 主站点和渠道站点统一入口
- 基于 `idx` 参数自动切换显示模式
- 渠道访问时显示渠道标识和信息

**pages/404.vue**:

- 用户友好的错误页面
- 有效渠道建议
- 返回主站和联系信息

#### 11.7.6 路由跳转逻辑
- **URL路径检测**: 自动从URL路径中提取渠道标识
- **有效渠道**: 在同一页面显示渠道版本内容
- **无效渠道**: 自动跳转到主页
- **缓存机制**: 5分钟渠道列表缓存
- **错误处理**: 完善的异常处理和用户反馈

#### 11.7.7 实现特点
- **统一入口**: 主站点和渠道站点使用同一个页面 (`pages/index.vue`)
- **路径识别**: 基于URL路径自动识别渠道，无需查询参数
- **动态显示**: 根据渠道访问状态动态显示内容和标识
- **简化架构**: 移除了独立的渠道页面，简化了路由配置

### 11.8 系统访问路径指南

#### 11.8.1 完整访问路径列表

**主要功能页面 (Hash模式 - 包含 /#/):**
- **移动端首页**: `http://localhost:8080/#/pages/index` (默认首页，带TabBar)
- **分类页面**: `http://localhost:8080/#/pages/category` (TabBar页面)
- **订单页面**: `http://localhost:8080/#/pages/orders` (TabBar页面)
- **我的页面**: `http://localhost:8080/#/pages/profile` (TabBar页面)
- **管理后台**: `http://localhost:8080/#/admin/index`
- **订单查看**: `http://localhost:8080/#/x/index`
- **404错误页**: `http://localhost:8080/#/pages/404`
- **G页面**: `http://localhost:8080/#/g/index`

**Hash模式访问路径:**

- **默认首页**: `http://localhost:8080/` → 自动跳转到 `/#/pages/index`
- **管理后台**: `http://localhost:8080/#/admin/index` ✅ (Hash模式)
- **渠道访问**: `http://localhost:8080/?channel=渠道ID` 或 `http://localhost:8080/#/pages/index?channel=渠道ID`

**渠道访问示例:**

- **渠道qudao1**: `http://localhost:8080/?channel=qudao1`
- **渠道123**: `http://localhost:8080/?channel=123`
- **Hash格式**: `http://localhost:8080/#/pages/index?channel=qudao1`

**TabBar导航系统:**

- **首页**: `http://localhost:8080/#/pages/index` (显示渠道信息，主要功能入口)
- **分类**: `http://localhost:8080/#/pages/category` (游戏陪玩服务分类)
- **订单**: `http://localhost:8080/#/pages/orders` (订单管理和查看)
- **我的**: `http://localhost:8080/#/pages/profile` (个人中心和设置)

**TabBar特性:**
- 固定在页面底部，所有TabBar页面都显示
- 使用项目icon文件夹下的图标文件
- 支持点击切换，流畅的页面切换体验
- 当前页面图标高亮显示

#### 11.8.2 管理后台访问方式

**本地开发环境 (Hash模式):**

```
http://localhost:8080/#/admin/index
```

**生产环境 (Hash模式):**

```
www.xxx.com/#/admin/index
```

**功能说明:**
- 完整的后台管理系统
- 包含用户管理、打手管理、订单管理等所有功能模块
- 左侧导航菜单，右侧内容区域
- 支持所有CRUD操作和系统配置

#### 11.8.3 路由配置状态

**已验证的有效路由:**
- ✅ `pages/index` → `pages/index.vue` (商场首页)
- ✅ `pages/404` → `pages/404.vue` (错误页面)
- ✅ `admin/index` → `admin/index.vue` (管理后台)
- ✅ `g/index` → `g/index.vue` (G页面)

**分包路由:**
- ✅ `x/index` → `x/index.vue` (订单查看系统)

**已移除的无效路由:**
- ❌ `x/login` (文件不存在，已从配置中移除)

#### 11.8.4 快速访问指南

**开发者常用路径 (Hash模式):**

1. **默认首页**: `http://localhost:8080/` → 自动跳转到移动端首页
2. **管理后台入口**: `http://localhost:8080/#/admin/index` ✅ (Hash模式)
3. **订单查看入口**: `http://localhost:8080/#/x/index` ✅ (Hash模式)

**渠道测试路径:**
- 测试有效渠道: `www.xxx.com/qudao1/` (需要在 allqudao 配置中存在)
- 测试无效渠道: `www.xxx.com/invalidchannel/` (会跳转到主页)

### 11.9 History模式路由优化

#### 11.9.1 技术实现
- **路由模式**: 从 `hash` 模式改为 `history` 模式
- **URL优化**: 移除了 `/#/` 路径，实现更简洁的URL
- **配置文件**: 修改 `manifest.json` 中的 `h5.router.mode` 配置

#### 11.9.2 URL格式对比

**优化前 (Hash模式):**
- 管理后台: `http://localhost:8080/#/admin/index`
- 渠道访问: `http://localhost:8080/#/pages/index?channel=123`

**优化后 (History模式):**
- 管理后台: `http://localhost:8080/admin/index` ✅
- 渠道访问: `http://localhost:8080/123` ✅

#### 11.9.3 服务器配置要求

**开发环境:**
- uniapp开发服务器自动处理History模式
- 无需额外配置

**生产环境 (Nginx配置示例):**
```nginx
server {
    listen 80;
    server_name www.xxx.com;
    root /var/www/html;
    index index.html;

    # 处理uniapp的history模式路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 处理API请求
    location /api/ {
        proxy_pass http://backend-server;
    }
}
```

**Apache配置示例:**
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>
```

#### 11.9.4 优势和注意事项

**优势:**
- ✅ URL更简洁美观，无 `/#/` 路径
- ✅ 更好的SEO支持
- ✅ 更符合现代Web应用标准
- ✅ 用户体验更好

**注意事项:**
- ⚠️ 需要服务器支持History模式
- ⚠️ 直接访问子路径时需要服务器正确配置
- ⚠️ 刷新页面时需要服务器返回index.html

### 11.10 移动端应用系统

#### 11.10.1 移动端页面结构
- **主入口页面**: `pages/mobile.vue` - 包含TabBar的移动端应用主页面
- **首页**: `pages/home.vue` - 移动端首页，包含功能入口
- **分类页面**: `pages/category.vue` - 服务分类展示
- **订单页面**: `pages/orders.vue` - 订单管理和查看
- **我的页面**: `pages/profile.vue` - 个人中心和设置

#### 11.10.2 TabBar导航配置
- **图标资源**: 使用 `/static/icon/` 下的图标文件
- **导航项目**:
  - 首页 (Home) - `shouye.png` / `shouye_active.png`
  - 分类 (Category) - `fenlei.png` / `fenlei_active.png`
  - 订单 (Orders) - `dingdan.png` / `dingdan_active.png`
  - 我的 (Profile) - `my.png` / `my_active.png`

#### 11.10.3 移动端适配特性
- **自动检测**: 自动检测移动设备并跳转到移动端页面
- **响应式布局**: 适配不同屏幕尺寸的移动设备
- **触摸优化**: 防止页面缩放和双击缩放
- **导航栏隐藏**: 使用 `navigationStyle: "custom"` 隐藏默认导航栏

#### 11.10.4 访问方式
- **直接访问**: `http://localhost:8080/pages/mobile`
- **手动访问**: 用户需要手动访问移动端页面（已移除自动跳转）
- **入口链接**: 在 `pages/index.vue` 首页提供"移动端"按钮入口
- **功能完整**: 每个TabBar页面都有完整的移动端UI设计

#### 11.10.5 路由配置变更
- **默认首页**: `pages/index.vue` 设为 `pages.json` 中的第一个页面
- **移动端页面**: `pages/mobile.vue` 保留但不作为默认首页
- **自动跳转**: 已移除移动设备自动跳转逻辑
- **访问行为**:
  - `http://localhost:8080/` → 直接显示商场首页
  - `http://localhost:8080/pages/mobile` → 手动访问移动端应用

### 11.11 数据格式兼容性
- **数组检查**: 所有列表数据都进行数组类型检查，确保ElTable组件正常工作
- **错误处理**: API请求失败时自动设置为空数组，避免类型错误

### 10.7 获取打手列表
- **接口**: `GET {{baseURL}}/api/user/search?userType=2&pageNum=1&pageSize=10`
- **参数说明**:
  - `userType=2`: 查询打手用户
  - `keyword`: 可选，搜索关键词
- **成功响应**: 返回用户列表格式

### 10.8 轮播图管理API接口

#### 10.8.1 获取轮播图列表
- **接口**: `GET {{baseURL}}/api/carousel?pageNum=1&pageSize=10`
- **排序规则**: 按权重（weight）从高到低自动排序

#### 10.8.2 添加轮播图
- **接口**: `POST {{baseURL}}/api/carousel`
- **请求参数**:
```json
{
  "name": "轮播图",
  "url": "图片URL地址",
  "weight": 2
}
```

#### 10.8.3 修改轮播图
- **接口**: `PUT {{baseURL}}/api/carousel`
- **请求参数**: 必须包含 `id` 字段
- **成功响应**:
```json
{
    "code": 1,
    "msg": "success",
    "data": null
}
```

#### 10.8.4 删除轮播图
- **接口**: `DELETE {{baseURL}}/api/carousel/{id}`
- **示例**: `DELETE {{baseURL}}/api/carousel/348726101213843456`

#### 10.8.5 获取单个轮播图
- **接口**: `GET {{baseURL}}/api/carousel/{id}`
- **示例**: `GET {{baseURL}}/api/carousel/348726101213843456`

#### 10.8.6 权重字段说明
- **weight字段**: 表示轮播图的权重/优先级
- **排序逻辑**: 系统会根据权重值从高到低自动排序显示轮播图
- **权重范围**: 1-999，权重越高显示优先级越高

### 10.9 渠道配置获取
- **接口**: `GET {{baseURL}}/api/system-config/get-by-key/{channelKey}`
- **渠道键值**: `qudao1`、`qudao2`、`qudao3` 等
- **成功响应**:
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "id": "348795348405325824",
        "configKey": "qudao1",
        "configValue": "123",
        "configDesc": "123",
        "createTime": "2025-08-20T19:48:11",
        "updateTime": "2025-08-20T19:48:11"
    }
}
```

### 10.9 订单查看和编辑功能说明
- **查看功能**: 独立页面模式，显示订单所有详细信息
- **编辑功能**: 独立页面模式，支持修改订单各个字段
- **字段映射修正**:
  - 订单号显示：使用 `id` 字段作为真正的订单号
  - 订单金额：通过 `productId` 调用商品详情接口获取 `money` 字段
  - 下单数量：使用 `totalAmount` 字段
  - 游戏内名称：使用 `orderNo` 字段
  - 游戏区服：使用 `gameName` 字段
  - 游戏内ID：使用 `gameServer` 字段
- **客户信息**: 面板标题改为"客户信息"，显示客户头像和昵称
- **打手信息**: 当 `assignType=1` 时加载打手列表，显示打手头像和昵称
- **截图处理**:
  - 显示时解析 `"[url1],[url2],[url3]"` 格式为图片数组
  - 编辑时支持上传新截图和删除现有截图
  - 保存时将图片数组转换回字符串格式
- **打手指定**: 支持搜索和选择打手，自动更新指定状态
- **密码显示**: 根据 `showPassword` 字段控制密码显示/隐藏
- **状态管理**: 支持修改支付状态和打手完成状态
- **时间显示**: 支付时间使用友好格式显示（今天、昨天、X天前等）
- **渠道信息**: 通过 `channelSource` 调用渠道配置接口获取 `configDesc` 显示
- **编辑页面优化**:
  - 打手选择：网格式打手卡片选择界面，显示头像和昵称
  - 打手详情：点击已选择的打手可查看详细信息
  - 金额计算：显示商品单价、下单数量和总金额
  - 客户留言：移动到游戏信息面板
  - 渠道编辑：支持修改渠道来源并实时更新显示
  - 密码字段：重新添加游戏密码的显示和编辑功能，支持显示/隐藏切换
- **界面美化优化**:
  - 系统配置：渠道管理表格添加"渠道ID"列显示 configKey 字段
  - 用户详情：美化打手/客户详情对话框，采用现代化卡片式布局
  - 视觉设计：渐变背景、圆角卡片、图标装饰、友好时间显示
  - 商品详情：订单编辑页面添加商品图片和富文本详情展示功能

## 注意事项

1. 所有ID字段使用字符串类型，以确保长整型精度
2. 时间字段格式为北京时间字符串
3. 文件上传支持 PDF、DOC、DOCX 格式
4. 所有接口都需要处理成功响应 (code === 1)
5. 分页参数从1开始计数
6. 商品详情和介绍字段支持HTML富文本格式，使用wangeditor编辑器
7. 订单管理中的截图字段需要解析字符串格式为图片数组
8. 订单状态使用数字标识，需要在前端转换为对应的文字和颜色标签
