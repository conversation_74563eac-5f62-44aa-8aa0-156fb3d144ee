<template>
	<view class="mobile-homepage">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在验证渠道...</text>
			</view>
		</view>

		<!-- 页面内容 -->
		<view v-else class="page-content">
			<!-- 渠道状态栏 -->
			<view class="channel-status-bar">
				<view v-if="isChannelAccess" class="channel-info">
					<text class="channel-label">当前渠道：</text>
					<text class="channel-name">{{ channelId }}</text>
				</view>
				<view v-else class="main-site-info">
					<text class="main-site-label">主站访问</text>
				</view>
			</view>

			<!-- 页面头部 -->
			<view class="header">
				<view class="header-content">
					<text class="app-title">三角洲陪玩商城</text>
					<text class="app-subtitle" v-if="isChannelAccess">{{ channelId }} 渠道专属</text>
					<text class="app-subtitle" v-else>专业游戏陪玩平台</text>
				</view>
			</view>

			<!-- 功能入口区域 -->
			<view class="function-section">
				<view class="function-grid">
					<view class="function-item" @click="goToAdmin">
						<view class="function-icon">🛠️</view>
						<text class="function-text">管理后台</text>
					</view>
					<view class="function-item" @click="goToOrderView">
						<view class="function-icon">📋</view>
						<text class="function-text">订单查看</text>
					</view>
					<view class="function-item">
						<view class="function-icon">🎮</view>
						<text class="function-text">游戏陪玩</text>
					</view>
					<view class="function-item">
						<view class="function-icon">💬</view>
						<text class="function-text">语音聊天</text>
					</view>
				</view>
			</view>

			<!-- 服务特色区域 -->
			<view class="features-section">
				<view class="section-title">
					<text class="title-text">服务特色</text>
				</view>
				<view class="features-list">
					<view class="feature-item">
						<view class="feature-icon">⭐</view>
						<view class="feature-content">
							<text class="feature-title">专业陪玩师</text>
							<text class="feature-desc">经验丰富，技术过硬</text>
						</view>
					</view>
					<view class="feature-item">
						<view class="feature-icon">🏆</view>
						<view class="feature-content">
							<text class="feature-title">快速代练</text>
							<text class="feature-desc">高效提升游戏等级</text>
						</view>
					</view>
					<view class="feature-item">
						<view class="feature-icon">💎</view>
						<view class="feature-content">
							<text class="feature-title">品质保证</text>
							<text class="feature-desc">严格的服务质量管控</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 渠道专属信息（仅渠道访问时显示） -->
			<view v-if="isChannelAccess" class="channel-exclusive-section">
				<view class="section-title">
					<text class="title-text">渠道专属</text>
				</view>
				<view class="exclusive-card">
					<view class="exclusive-content">
						<text class="exclusive-title">{{ channelId }} 渠道用户专享</text>
						<text class="exclusive-desc">享受与主站完全一致的优质服务</text>
						<text class="exclusive-desc">专属客服支持，贴心服务体验</text>
					</view>
				</view>
			</view>

			<!-- 页面底部 -->
			<view class="footer">
				<text class="footer-text">© 2025 三角洲陪玩商城</text>
				<text v-if="isChannelAccess" class="footer-channel">{{ channelId }} 渠道</text>
			</view>
		</view>

		<!-- 底部TabBar导航 -->
		<view class="tabbar">
			<view class="tabbar-item active" @click="switchTab('index')">
				<image class="tabbar-icon" src="/static/icon/shouye_active.png"></image>
				<text class="tabbar-text active">首页</text>
			</view>
			<view class="tabbar-item" @click="switchTab('category')">
				<image class="tabbar-icon" src="/static/icon/fenlei.png"></image>
				<text class="tabbar-text">分类</text>
			</view>
			<view class="tabbar-item" @click="switchTab('orders')">
				<image class="tabbar-icon" src="/static/icon/dingdan.png"></image>
				<text class="tabbar-text">订单</text>
			</view>
			<view class="tabbar-item" @click="switchTab('profile')">
				<image class="tabbar-icon" src="/static/icon/my.png"></image>
				<text class="tabbar-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	import channelValidator from '../utils/channelValidator.js'

	export default {
		name: 'MallHomepage',
		data() {
			return {
				loading: false,
				isChannelAccess: false,
				channelId: '',
				validChannels: []
			}
		},
		async onLoad() {
			// 从URL路径中提取渠道信息
			this.loading = true
			await this.checkChannelFromUrl()
		},
		onShow() {
			// 页面显示时的逻辑
		},
		methods: {
			// 从URL检测渠道 (适配Hash模式)
			async checkChannelFromUrl() {
				try {
					// Hash模式下，从URL参数中提取渠道信息
					const fullUrl = window.location.href
					console.log('当前完整URL:', fullUrl)

					// 方法1: 检查URL参数中的channel参数
					const urlParams = new URLSearchParams(window.location.search)
					let channelId = urlParams.get('channel')

					// 方法2: 从hash中提取渠道信息
					if (!channelId && window.location.hash) {
						const hashParams = window.location.hash.split('?')[1]
						if (hashParams) {
							const hashUrlParams = new URLSearchParams(hashParams)
							channelId = hashUrlParams.get('channel')
						}
					}

					// 方法3: 使用channelValidator的extractChannelFromUrl方法
					if (!channelId) {
						channelId = channelValidator.extractChannelFromUrl(fullUrl)
					}

					if (channelId) {
						console.log('检测到渠道ID:', channelId)
						await this.validateChannelAccess(channelId)
					} else {
						// 没有检测到渠道，显示标准首页
						console.log('未检测到渠道，显示主站首页')
						this.loading = false
					}
				} catch (error) {
					console.error('URL渠道检测失败:', error)
					this.loading = false
				}
			},

			// 验证渠道访问
			async validateChannelAccess(channelId) {
				try {
					console.log('验证渠道访问:', channelId)

					// 验证渠道是否有效
					const isValid = await channelValidator.validateChannel(channelId)

					if (isValid) {
						// 有效渠道，显示渠道版本页面
						this.isChannelAccess = true
						this.channelId = channelId

						// 更新页面标题
						uni.setNavigationBarTitle({
							title: `${channelId} - 三角洲陪玩商城`
						})

						console.log('渠道验证成功:', channelId)
					} else {
						// 无效渠道，显示标准首页
						console.log('渠道验证失败，显示标准首页:', channelId)
						this.isChannelAccess = false
						this.channelId = ''
					}
				} catch (error) {
					console.error('渠道验证过程失败:', error)
					// 验证失败，显示标准首页
					this.isChannelAccess = false
					this.channelId = ''
				} finally {
					this.loading = false
				}
			},

			// 跳转到管理后台
			goToAdmin() {
				uni.navigateTo({
					url: '/admin/index'
				})
			},

			// 跳转到订单查看页面
			goToOrderView() {
				uni.navigateTo({
					url: '/x/index'
				})
			},

			// TabBar切换方法
			switchTab(tabName) {
				console.log('切换到:', tabName)
				switch(tabName) {
					case 'index':
						// 当前就是首页，不需要跳转
						break
					case 'category':
						uni.switchTab({
							url: '/pages/category'
						})
						break
					case 'orders':
						uni.switchTab({
							url: '/pages/orders'
						})
						break
					case 'profile':
						uni.switchTab({
							url: '/pages/profile'
						})
						break
				}
			}
		}
	}
</script>

<style scoped>
	.mobile-homepage {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #333;
	}

	/* 加载状态样式 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.loading-content {
		text-align: center;
		padding: 60rpx;
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid rgba(102, 126, 234, 0.2);
		border-top: 6rpx solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 30rpx;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		color: #666;
		font-size: 28rpx;
		display: block;
	}

	.page-content {
		min-height: 100vh;
		background-color: #f8f9fa;
	}

	/* 渠道状态栏 */
	.channel-status-bar {
		background-color: rgba(255, 255, 255, 0.95);
		padding: 20rpx 40rpx;
		text-align: center;
		border-bottom: 2rpx solid rgba(255, 255, 255, 0.3);
	}

	.channel-info {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 10rpx;
	}

	.channel-label {
		color: #666;
		font-size: 28rpx;
	}

	.channel-name {
		color: #667eea;
		font-size: 32rpx;
		font-weight: bold;
		background-color: rgba(102, 126, 234, 0.1);
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
	}

	.main-site-info {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.main-site-label {
		color: #28a745;
		font-size: 32rpx;
		font-weight: bold;
		background-color: rgba(40, 167, 69, 0.1);
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
	}

	/* 页面头部 */
	.header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 60rpx 40rpx;
		text-align: center;
		color: white;
	}

	.app-title {
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 20rpx;
	}

	.app-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
		display: block;
	}

	/* 功能入口区域 */
	.function-section {
		padding: 40rpx;
		background-color: #f8f9fa;
	}

	.function-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 30rpx;
	}

	.function-item {
		background-color: white;
		border-radius: 20rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.3s ease;
	}

	.function-item:active {
		transform: scale(0.95);
	}

	.function-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
		display: block;
	}

	.function-text {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		display: block;
	}

	/* 服务特色区域 */
	.features-section {
		padding: 40rpx;
		background-color: white;
	}

	.section-title {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.title-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
	}

	.feature-item {
		display: flex;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 2rpx solid #f0f0f0;
	}

	.feature-item:last-child {
		border-bottom: none;
	}

	.feature-icon {
		font-size: 48rpx;
		margin-right: 30rpx;
		width: 80rpx;
		text-align: center;
	}

	.feature-content {
		flex: 1;
	}

	.feature-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}

	.feature-desc {
		font-size: 26rpx;
		color: #666;
		display: block;
	}

	/* 渠道专属区域 */
	.channel-exclusive-section {
		padding: 40rpx;
		background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	}

	.exclusive-card {
		background-color: rgba(255, 255, 255, 0.9);
		border-radius: 20rpx;
		padding: 40rpx;
		text-align: center;
	}

	.exclusive-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #d63031;
		display: block;
		margin-bottom: 20rpx;
	}

	.exclusive-desc {
		font-size: 28rpx;
		color: #666;
		display: block;
		margin-bottom: 15rpx;
		line-height: 1.5;
	}

	/* 页面底部 */
	.footer {
		background-color: #2d3748;
		color: white;
		text-align: center;
		padding: 40rpx;
		margin-top: 40rpx;
	}

	.footer-text {
		font-size: 26rpx;
		display: block;
		margin-bottom: 10rpx;
	}

	.footer-channel {
		font-size: 24rpx;
		color: #ffd700;
		display: block;
	}

	/* 响应式设计 */
	@media (max-width: 750px) {
		.function-grid {
			grid-template-columns: 1fr 1fr;
			gap: 20rpx;
		}

		.function-item {
			padding: 30rpx 15rpx;
		}

		.function-icon {
			font-size: 50rpx;
		}

		.function-text {
			font-size: 26rpx;
		}

		.app-title {
			font-size: 42rpx;
		}

		.app-subtitle {
			font-size: 26rpx;
		}

		.channel-name {
			font-size: 28rpx;
		}

		.main-site-label {
			font-size: 28rpx;
		}
	}

	@media (max-width: 480px) {
		.function-grid {
			grid-template-columns: 1fr;
			gap: 20rpx;
		}

		.header {
			padding: 40rpx 30rpx;
		}

		.function-section {
			padding: 30rpx;
		}

		.features-section {
			padding: 30rpx;
		}

		.channel-exclusive-section {
			padding: 30rpx;
		}
	}

	/* 底部TabBar样式 */
	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #e5e5e5;
		display: flex;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		transition: all 0.3s ease;
	}

	.tabbar-item.active {
		transform: scale(1.05);
	}

	.tabbar-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}

	.tabbar-text {
		font-size: 24rpx;
		color: #999999;
		transition: color 0.3s ease;
	}

	.tabbar-text.active {
		color: #007AFF;
		font-weight: 500;
	}

	/* 为TabBar预留底部空间 */
	.page-content {
		padding-bottom: 120rpx;
	}
</style>
